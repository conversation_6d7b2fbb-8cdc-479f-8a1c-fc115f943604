import 'dart:io';
import 'package:audioplayers/audioplayers.dart' as ap;

import 'package:audio_session/audio_session.dart';
import 'package:connectone/bai_models/bai_products_res.dart';
import 'package:connectone/bai_models/seller_offers_res.dart' as sor;
import 'package:connectone/bai_models/seller_offers_res.dart';
import 'package:connectone/core/bai_widgets/bai_button.dart';
import 'package:connectone/core/bai_widgets/bai_image.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';
import 'package:url_launcher/url_launcher.dart';

class SellerCard extends StatefulWidget {
  final int number;
  final TextEditingController offerController;
  final TextEditingController finalofferController;
  final TextEditingController commentController;
  final TextEditingController quantityController;
  SellerOffer offer;
  final Content item;
  final bool showQuantity;
  final String quantity;
  final String? unit;

  final AlreadySubmittedQuote? submittedQuote;
  final PreviousQuote? previousQuote;

  final List<String> images;
  final List<String> files;
  final List<String> audios;

  final List<Uint8List> imageBytes;
  final List<Uint8List> fileBytes;

  final Function(List<String>) onImagesUpdated;
  final Function(List<String>) onFilesUpdated;
  final Function(List<String>) onAudiosUpdated;
  final Function(List<Uint8List>) onImageBytesUpdated;
  final Function(List<Uint8List>) onFileBytesUpdated;

  final Function onSubmit;
  final double? gst;

  SellerCard({
    Key? key,
    required this.finalofferController,
    required this.number,
    required this.offerController,
    required this.commentController,
    required this.quantityController,
    required this.offer,
    required this.item,
    required this.showQuantity,
    required this.quantity,
    required this.unit,
    required this.onImagesUpdated,
    required this.onFilesUpdated,
    required this.onAudiosUpdated,
    required this.onImageBytesUpdated,
    required this.onFileBytesUpdated,
    required this.images,
    required this.files,
    required this.audios,
    required this.imageBytes,
    required this.fileBytes,
    this.submittedQuote,
    this.previousQuote,
    required this.onSubmit,
    this.gst,
  }) : super(key: key);

  @override
  State<SellerCard> createState() => _SellerCardState();
}

class _SellerCardState extends State<SellerCard>
    with AutomaticKeepAliveClientMixin {
  Codec _codec = Codec.aacMP4;
  final String _mPath = 'audio_${DateTime.now().millisecondsSinceEpoch}.mp4';
  FlutterSoundPlayer? _mPlayer = FlutterSoundPlayer();
  FlutterSoundRecorder? _mRecorder = FlutterSoundRecorder();
  var theSource = AudioSource.microphone;
  bool _mPlayerIsInited = false;
  bool _mRecorderIsInited = false;
  bool _mplaybackReady = false;

  final bool _submitNewQuotePressed = false;
  final bool _submitPreviousQuotePressed = false;

  List<String> images = [];
  List<String> files = [];
  List<String> audios = [];

  final List<Uint8List> _imageBytes = [];
  final List<Uint8List> _fileBytes = [];

  bool isSteelReinforcement() {
    return !widget.showQuantity;
  }

  bool hasAttachmentsMaxed() {
    return widget.images.length + widget.files.length + widget.audios.length >=
        5;
  }

  final ImagePicker picker = ImagePicker();
  bool recording = false;

  bool playing = false;

  final player = ap.AudioPlayer();
  bool isHttpsUrl(String url) {
    return url.startsWith('http://') || url.startsWith('https://');
  }

  bool _initialBrandWasAny = false; // Store if initial brand was "any"

  @override
  void dispose() {
    _mPlayer!.closePlayer();
    _mPlayer = null;
    _mRecorder!.closeRecorder();
    _mRecorder = null;
    super.dispose();
  }

  void record() {
    _mRecorder!
        .startRecorder(
      toFile: _mPath,
      codec: _codec,
      audioSource: theSource,
    )
        .then((value) {
      setState(() {});
    });
  }

  pickFiles() async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
        allowMultiple: false,
        withData: true, // Retrieve file bytes directly
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile file = result.files.first;

        if (kIsWeb) {
          // Web: Use bytes
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              widget.fileBytes.add(file.bytes!);
              widget.files.add(file.name);
              widget.onFilesUpdated(widget.files);
              widget.onFileBytesUpdated(widget.fileBytes);
            });
          }
        } else {
          // Non-web: Use bytes, can optionally use path if needed
          if (file.bytes != null) {
            setState(() {
              _fileBytes.add(file.bytes!);
              widget.fileBytes.add(file.bytes!);
              widget.files.add(file.path ?? file.name);
              widget.onFilesUpdated(widget.files);
              widget.onFileBytesUpdated(widget.fileBytes);
            });
          } else if (file.path != null) {
            File selectedFile = File(file.path!);
            setState(() {
              widget.files.add(file.path!);
              widget.onFilesUpdated(widget.files);
              selectedFile.readAsBytes().then((value) {
                _fileBytes.add(value);
                widget.fileBytes.add(value);
                widget.onFileBytesUpdated(widget.fileBytes);
              });
            });
          }
        }
      }
    }
  }

  void stopRecorder() async {
    await _mRecorder!.stopRecorder().then((value) {
      setState(() {
        //var url = value;
        if (hasAttachmentsMaxed()) {
          widget.audios.clear();
          _mplaybackReady = true;
          alert(
              "You can attach a maximum of 5 files, including images, documents, and audio. Please remove some to add new ones.");
        } else {
          widget.audios.clear();
          if (value != null) {
            widget.audios.add(value);
            widget.onAudiosUpdated(widget.audios);
          }
          _mplaybackReady = true;
        }
      });
    });
  }

  void play() {
    assert(_mPlayerIsInited &&
        _mplaybackReady &&
        _mRecorder!.isStopped &&
        _mPlayer!.isStopped);
    _mPlayer!
        .startPlayer(
            fromURI: _mPath,
            whenFinished: () {
              setState(() {});
            })
        .then((value) {
      setState(() {});
    });
  }

  void stopPlayer() {
    _mPlayer!.stopPlayer().then((value) {
      setState(() {});
    });
  }

  Future<void> _togglePlayback() async {
    try {
      if (playing) {
        await player.stop();
        setState(() {
          playing = false;
        });
      } else {
        await player.play(ap.UrlSource(audios[0]));
        setState(() {
          playing = true;
        });
        player.onPlayerComplete.listen((event) {
          setState(() {
            playing = false;
          });
        });
      }
    } catch (e) {
      alert(e.toString());
    }
  }

  getRecorderFn() {
    if (!_mRecorderIsInited || !_mPlayer!.isStopped) {
      return null;
    }

    return _mRecorder!.isStopped ? record() : stopRecorder();
  }

  getPlaybackFn() {
    if (!_mPlayerIsInited || !_mplaybackReady || !_mRecorder!.isStopped) {
      return null;
    }

    return _mPlayer!.isStopped ? play() : stopPlayer();
  }

  Future<void> getImage({bool fromCamera = true}) async {
    if (hasAttachmentsMaxed()) {
      alert(
          "You can attach a maximum of 5 files,Please remove some to add new ones.");
    } else {
      try {
        final XFile? image = await picker.pickImage(
          source: fromCamera ? ImageSource.camera : ImageSource.gallery,
          requestFullMetadata: false,
          imageQuality: 25,
        );
        if (image != null) {
          final Uint8List imageBytes = await image.readAsBytes();

          if (mounted) {
            setState(() {
              _imageBytes.add(imageBytes);
              widget.imageBytes.add(imageBytes);
              widget.images.add(image.path);
              widget.onImagesUpdated(widget.images);
              widget.onImageBytesUpdated(widget.imageBytes);
            });
          }
        }
      } catch (e) {
        safePrint(e);
      }
    }
  }

  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Select Image Source",
              style: TextStyle(fontWeight: FontWeight.bold)),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                GestureDetector(
                  child: const Text("Take using Camera"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: true);
                  },
                ),
                const SizedBox(height: 16),
                GestureDetector(
                  child: const Text("Pick from Gallery"),
                  onTap: () {
                    Navigator.of(context).pop();
                    getImage(fromCamera: false);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool isExpanded = false;

  Future<void> openTheRecorder() async {
    if (!kIsWeb) {
      var status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        throw RecordingPermissionException(
            'Microphone permission not granted.');
      }
    }
    await _mRecorder!.openRecorder();
    if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
      _codec = Codec.opusWebM;
      if (!await _mRecorder!.isEncoderSupported(_codec) && kIsWeb) {
        _mRecorderIsInited = true;
        return;
      }
    }
    final session = await AudioSession.instance;
    await session.configure(AudioSessionConfiguration(
      avAudioSessionCategory: AVAudioSessionCategory.playAndRecord,
      avAudioSessionCategoryOptions:
          AVAudioSessionCategoryOptions.allowBluetooth |
              AVAudioSessionCategoryOptions.defaultToSpeaker,
      avAudioSessionMode: AVAudioSessionMode.spokenAudio,
      avAudioSessionRouteSharingPolicy:
          AVAudioSessionRouteSharingPolicy.defaultPolicy,
      avAudioSessionSetActiveOptions: AVAudioSessionSetActiveOptions.none,
      androidAudioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.speech,
        flags: AndroidAudioFlags.none,
        usage: AndroidAudioUsage.voiceCommunication,
      ),
      androidAudioFocusGainType: AndroidAudioFocusGainType.gain,
      androidWillPauseWhenDucked: true,
    ));

    _mRecorderIsInited = true;
  }

  @override
  void initState() {
    super.initState();
    _mPlayer!.openPlayer().then((value) {
      setState(() {
        _mPlayerIsInited = true;
      });
    });

    openTheRecorder().then((value) {
      setState(() {
        _mRecorderIsInited = true;
      });
    });

    _setValues();
    _setArrays(widget.submittedQuote);

    // Check if initial brand name was "any" (case-insensitive)
    if (widget.offer.variant1OptionName?.toLowerCase() == "any") {
      _initialBrandWasAny = true;
    } else {
      _initialBrandWasAny = false;
    }
  }

  void _setArrays(AlreadySubmittedQuote? alreadySubmittedQuote) {
    final List<sor.Media> items = alreadySubmittedQuote?.medias ?? [];
    for (var media in items) {
      final String? url = media.url;
      if (url == null) {
        continue;
      }
      if (_isImage(url)) {
        images.add(url);
      } else if (_isAudio(url)) {
        audios.add(url);
      } else {
        files.add(url);
      }
    }
  }

  static bool _isImage(String url) {
    return url.endsWith('.jpg') ||
        url.endsWith('.jpeg') ||
        url.endsWith('.png') ||
        url.endsWith('.gif') ||
        url.endsWith('.bmp');
  }

  static bool _isAudio(String url) {
    return url.endsWith('.mp3') ||
        url.endsWith('.wav') ||
        url.endsWith('.aac') ||
        url.endsWith('.ogg') ||
        url.endsWith('.mp4');
  }

  var currencyFormatter =
      NumberFormat.currency(locale: 'en_IN', symbol: '₹', decimalDigits: 2);

  @override

  /// Builds a widget for displaying a quote with editable fields for the user to update and submit a new quote.
  Widget build(BuildContext context) {
    super.build(context);
    var style = const TextStyle(fontWeight: FontWeight.bold);

    void updateFinalOffer() {
      var offerText = cleanRupeeString(widget.offerController.text);
      var price = double.tryParse(offerText) ?? 0;
      var quantity1 = double.tryParse(widget.quantityController.text) ?? 0;
      var sum = price * quantity1;
      widget.finalofferController.text = currencyFormatter.format(sum);
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
          boxShadow: const [
            // BoxShadow(
            //   color: AppColors.primaryColor.withOpacity(0.2),
            //   offset: const Offset(0, 2),
            //   blurRadius: 6,
            //   spreadRadius: 2,
            // ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.offer.variant1OptionName != null &&
                  widget.offer.variant1OptionName!.isNotEmpty)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildOptionRowWithEdit(
                      // Use the new widget here
                      widget.offer.variant1OptionGroupName,
                      widget.offer.variant1OptionName,
                      _initialBrandWasAny,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${widget.number}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              if (widget.offer.variant2OptionName != null &&
                  widget.offer.variant2OptionName!.isNotEmpty) ...[
                const SizedBox(height: 2),
                SizedBox(
                  height: 16,
                  child: _buildOptionRow(
                    widget.offer.variant2OptionGroupName,
                    widget.offer.variant2OptionName,
                  ),
                )
              ],
              if (widget.offer.variant3OptionName != null &&
                  widget.offer.variant3OptionName!.isNotEmpty) ...[
                const SizedBox(height: 2),
                SizedBox(
                  height: 16,
                  child: _buildOptionRow(
                    widget.offer.variant3OptionGroupName,
                    widget.offer.variant3OptionName,
                  ),
                )
              ],
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 2,
                    child:
                        Text('Quantity (${widget.unit ?? ""})', style: style),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 3,
                    child: TextField(
                      controller: widget.quantityController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      style: const TextStyle(color: Colors.black),
                      maxLines: 1,
                      enabled: !_alreadySubmitted,
                      onChanged: (val) => updateFinalOffer(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Price / ${widget.unit}',
                          style: style,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    flex: 3,
                    child: TextField(
                      controller: widget.offerController,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      // inputFormatters: [IndianRupeeFormatter()],
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      style: const TextStyle(color: Colors.black),
                      maxLines: 1,
                      enabled: !_alreadySubmitted,
                      onChanged: (val) => updateFinalOffer(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 2,
                    child:
                        Text('Total Price\n(Quantity x Price)', style: style),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 3,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: TextField(
                            controller: widget.finalofferController,
                            keyboardType: const TextInputType.numberWithOptions(
                                decimal: true),
                            // inputFormatters: [IndianRupeeFormatter()],
                            decoration: InputDecoration(
                              border: const OutlineInputBorder(),
                              isDense: true,
                              helperText: _alreadySubmitted
                                  ? 'Already submitted rate. You cannot edit it.'
                                  : _previouslySubmitted
                                      ? 'Last submitted rate based on your previous submission. You can edit it.'
                                      : null,
                            ),
                            style: (_alreadySubmitted || _previouslySubmitted)
                                ? const TextStyle(
                                    color: Colors.blue,
                                    fontWeight: FontWeight.bold)
                                : const TextStyle(color: Colors.black),
                            maxLines: 1,
                            enabled: !_alreadySubmitted && _previouslySubmitted
                                ? true
                                : false,
                          ),
                        ),
                        if (_alreadySubmitted || _previouslySubmitted) ...[
                          const SizedBox(width: 4),
                          GestureDetector(
                            onTap: () {
                              if (_alreadySubmitted) {
                                properAlert(
                                    'You have already submitted this rate. \n\nYou cannot edit it.');
                              } else if (_previouslySubmitted) {
                                properAlert(
                                    'This is the last submitted rate based on your previous submission. \n\nYou can edit it.');
                              }
                            },
                            child: const Padding(
                              padding: EdgeInsets.only(bottom: 20, left: 4),
                              child: Icon(Icons.info_outline,
                                  color: Colors.blue, size: 18),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              // GST calculation fields - only show for buyers
              if (widget.gst != null) ...[
                const SizedBox(height: 12),
                // Row(
                //   children: [
                //     Expanded(
                //       flex: 2,
                //       child: Text(
                //         'GST %:',
                //         style:
                //             style.copyWith(backgroundColor: Colors.greenAccent),
                //       ),
                //     ),
                //     Expanded(
                //       flex: 3,
                //       child: Text(
                //         '${widget.gst!.toStringAsFixed(2)}%',
                //         style:
                //             style.copyWith(backgroundColor: Colors.greenAccent),
                //       ),
                //     ),
                //   ],
                // ),
                // const SizedBox(height: 8),
                // Quote without GST (uneditable box)
                const Text(
                  'Quote (w/o GST)',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.green.shade50,
                  ),
                  child: Text(
                    '₹${NumberFormat('#,##0.00').format(_calculateQuoteWithoutGst())}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Quote with GST (distinct and prominent box)
                const Text(
                  'Quote (with GST)',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey.shade400,
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.green.shade50,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₹${NumberFormat('#,##0.00').format(_calculateQuoteWithGst())}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 16),
              if (widget.submittedQuote != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.green.shade200,
                  ),
                  child: Text(
                    "Already submitted quote on \n${widget.submittedQuote?.createdAt?.toCreatedOn()}",
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              if (_previouslySubmitted && !_submitNewQuotePressed)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Colors.green.shade200,
                  ),
                  child: Text(
                    "Latest Submitted Quote - \n${widget.previousQuote?.createdAt?.toCreatedOn()}",
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              Center(
                child: TextButton(
                  onPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                  child: Text(
                    isExpanded ? "Hide" : "Show more",
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Visibility(
                visible: isExpanded,
                child: Column(
                  children: [
                    if (!_alreadySubmitted) const SizedBox(height: 12),
                    if (!_alreadySubmitted)
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade400,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Upload / Attach Photo",
                                style: style,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  if (widget.images.isNotEmpty &&
                                      images.isEmpty)
                                    Flexible(
                                      child: SizedBox(
                                        height: 76,
                                        child: ListView.separated(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: widget.images.length,
                                          padding: const EdgeInsets.all(6),
                                          itemBuilder: (context, index) {
                                            return Stack(
                                              clipBehavior: Clip.none,
                                              children: [
                                                Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  clipBehavior: Clip.hardEdge,
                                                  child: kIsWeb
                                                      ? Image.memory(
                                                          _imageBytes[index],
                                                          width: 64,
                                                          height: 64,
                                                          fit: BoxFit.cover,
                                                        )
                                                      : Image.file(
                                                          File(widget
                                                              .images[index]),
                                                          width: 64,
                                                          height: 64,
                                                          fit: BoxFit.cover,
                                                        ),
                                                ),
                                                Positioned(
                                                  top: -8,
                                                  right: -8,
                                                  child: InkWell(
                                                    onTap: () {
                                                      setState(() {
                                                        widget.images
                                                            .removeAt(index);
                                                        widget.onImagesUpdated(
                                                            widget.images);
                                                      });
                                                    },
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            color: Colors.red),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16),
                                                        color:
                                                            Colors.transparent,
                                                      ),
                                                      padding:
                                                          const EdgeInsets.all(
                                                              2),
                                                      child: const Icon(
                                                        Icons.close,
                                                        size: 16,
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return const SizedBox(width: 12);
                                          },
                                        ),
                                      ),
                                    ),
                                  if (images.isEmpty)
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color: Colors.grey.shade200,
                                      ),
                                      child: IconButton(
                                        icon: const Icon(Icons.photo),
                                        onPressed: () {
                                          setState(() {
                                            showImageSourceDialog(context);
                                          });
                                        },
                                      ),
                                    )
                                ],
                              ),
                              if (images.isNotEmpty) const SizedBox(height: 16),
                            ],
                          ),
                        ),
                      ),
                    if (images.isNotEmpty) const SizedBox(height: 12),
                    if (images.isNotEmpty)
                      // Text('data'),
                      SizedBox(
                        height: 74,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemCount: images.length,
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(2),
                          itemBuilder: (context, index) {
                            return Material(
                              elevation: 2,
                              borderRadius: BorderRadius.circular(8.0),
                              clipBehavior: Clip.hardEdge,
                              child: GestureDetector(
                                onTap: () {
                                  showImageDialog(context, images[index]);
                                },
                                child: Container(
                                  margin: const EdgeInsets.all(0.25),
                                  child: Image.network(
                                    images[index],
                                    width: 72.0,
                                    height: 72.0,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              // Image.network(images[index], fit: BoxFit.fill),
                            );
                          },
                          separatorBuilder: (BuildContext context, int index) {
                            return const SizedBox(width: 8);
                          },
                        ),
                      ),
                    if (!_alreadySubmitted) const SizedBox(height: 12),
                    //audio and doc
                    if (!_alreadySubmitted)
                      Container(
                        // height: 56,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade400,
                          ),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 8),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                      child: Text(
                                    widget.audios.isNotEmpty
                                        ? "Voice Note Added"
                                        : "Add Voice Note",
                                    style: style,
                                  )),
                                  const Icon(Icons.mic),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(children: [
                                SizedBox(
                                  width: kIsWeb
                                      ? 100
                                      : MediaQuery.of(context).size.width *
                                          0.25,
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            AppColors.primaryColor),
                                    onPressed: () {
                                      getRecorderFn();
                                    },
                                    child: Text(_mRecorder!.isRecording
                                        ? 'Stop'
                                        : 'Record'),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(_mRecorder!.isRecording
                                    ? 'Recording in progress'
                                    : widget.audios.isNotEmpty
                                        ? 'Discard & record again'
                                        : 'Recorder is stopped'),
                              ]),
                            ],
                          ),
                        ),
                      ),
                    if (!_alreadySubmitted && widget.audios.isNotEmpty)
                      const SizedBox(height: 12),
                    if (!_alreadySubmitted && widget.audios.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(8),
                        height: 80,
                        width: double.infinity,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade400,
                          ),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: kIsWeb
                                  ? 100
                                  : MediaQuery.of(context).size.width * 0.25,
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primaryColor),
                                onPressed: () {
                                  isHttpsUrl(widget.audios[0])
                                      ? _togglePlayback()
                                      : getPlaybackFn();
                                },
                                child: Text((_mPlayer!.isPlaying || playing)
                                    ? 'Stop'
                                    : 'Play'),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text((_mPlayer!.isPlaying || playing)
                                ? 'Playback in progress'
                                : 'Play recorded audio'),
                            const Spacer(),
                            IconButton(
                                onPressed: () {
                                  setState(() {
                                    widget.audios.clear();
                                    widget.onAudiosUpdated(widget.audios);
                                  });
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                )),
                          ],
                        ),
                      ),
                    if (audios.isNotEmpty) const SizedBox(height: 12),
                    if (audios.isNotEmpty)
                      Row(
                        children: [
                          GestureDetector(
                            onTap: _togglePlayback,
                            child: Icon(
                              playing ? Icons.pause : Icons.play_circle_fill,
                              size: 40.0,
                              color: AppColors.primaryColorOld,
                            ),
                          ),
                          const SizedBox(width: 8.0),
                          Container(
                            width: 200.0,
                            height: 40.0,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 0, vertical: 4),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Image.asset("assets/images/waveform.png"),
                          ),
                        ],
                      ),
                    if (!_alreadySubmitted) const SizedBox(height: 16),
                    if (!_alreadySubmitted)
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.shade400,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Upload Additional Specifications / Docs",
                                style: style,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  if (widget.files.isNotEmpty)
                                    Flexible(
                                      child: SizedBox(
                                        height: 76,
                                        child: ListView.separated(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: widget.files.length,
                                          padding: const EdgeInsets.all(6),
                                          itemBuilder: (context, index) {
                                            return Stack(
                                              clipBehavior: Clip.none,
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                    color: Colors.grey.shade200,
                                                  ),
                                                  child: IconButton(
                                                    icon: Icon(
                                                        Icons
                                                            .file_copy_outlined,
                                                        color: AppColors
                                                            .primaryColor),
                                                    onPressed: () {},
                                                  ),
                                                ),
                                                Positioned(
                                                  top: -8,
                                                  right: -8,
                                                  child: GestureDetector(
                                                    onTap: () {
                                                      setState(() {
                                                        widget.files
                                                            .removeAt(index);
                                                        widget.onFilesUpdated(
                                                            widget.files);
                                                      });
                                                    },
                                                    child: Container(
                                                      decoration: BoxDecoration(
                                                        border: Border.all(
                                                            color: Colors.red),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16),
                                                      ),
                                                      padding:
                                                          const EdgeInsets.all(
                                                              2),
                                                      child: const Icon(
                                                        Icons.close,
                                                        size: 16,
                                                        color: Colors.red,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            );
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return const SizedBox(width: 12);
                                          },
                                        ),
                                      ),
                                    ),
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      color: Colors.grey.shade200,
                                    ),
                                    child: IconButton(
                                      icon: const Icon(
                                          Icons.file_upload_outlined),
                                      onPressed: () {
                                        setState(() {
                                          pickFiles();
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    if (files.isNotEmpty) const SizedBox(height: 12),
                    if (files.isNotEmpty)
                      SizedBox(
                        height: 74,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemCount: files.length,
                          shrinkWrap: true,
                          padding: const EdgeInsets.all(2),
                          itemBuilder: (context, index) {
                            var url = Uri.parse(files[index]);
                            return GestureDetector(
                              onTap: () async {
                                if (await canLaunchUrl(url)) {
                                  await launchUrl(url);
                                } else {
                                  throw 'Could not launch $url';
                                }
                              },
                              child: Material(
                                elevation: 2,
                                borderRadius: BorderRadius.circular(8.0),
                                clipBehavior: Clip.hardEdge,
                                child: Container(
                                  color: Colors.grey.shade200,
                                  height: 72,
                                  width: 72,
                                  margin: const EdgeInsets.all(0.25),
                                  child: Center(
                                      child: Icon(Icons.file_copy_outlined,
                                          size: 32.0,
                                          color: AppColors.primaryColor)),
                                ),
                              ),
                            );
                          },
                          separatorBuilder: (BuildContext context, int index) {
                            return const SizedBox(width: 8);
                          },
                        ),
                      ),
                    const SizedBox(height: 12),
                    Text('Remarks', style: style),
                    const SizedBox(height: 4),
                    TextField(
                      controller: widget.commentController,
                      maxLines: 2,
                      maxLength: 500,
                      enabled: !_alreadySubmitted,
                      decoration: const InputDecoration(
                        isDense: true,
                        border: OutlineInputBorder(),
                      ),
                      style: const TextStyle(color: Colors.black),
                    ),
                  ],
                ),
              ),
              if (!_alreadySubmitted && _previouslySubmitted && !valuesCleared)
                const SizedBox(height: 12),
              if (!_alreadySubmitted && _previouslySubmitted && !valuesCleared)
                BaiButton(
                  onTap: () {
                    setState(() {
                      _clearValues();
                    });
                  },
                  height: 40,
                  text: "CLEAR PREVIOUS QUOTE",
                  backgoundColor: _submitNewQuotePressed
                      ? Colors.grey
                      : Colors.green.shade800,
                ),
            ],
          ),
        ),
      ),
    );
  }

  bool get _alreadySubmitted => widget.submittedQuote != null;

  bool get _previouslySubmitted {
    return widget.previousQuote != null && widget.submittedQuote == null;
  }

  _setValues() {
    if (_alreadySubmitted) {
      widget.commentController.text =
          (widget.submittedQuote?.remarks?.isNotEmpty ?? false)
              ? widget.submittedQuote!.remarks!
              : "N/A";
      widget.finalofferController.text =
          widget.submittedQuote?.offerPrice.toString() ?? "0";
      var offerText = cleanRupeeString(widget.finalofferController.text);
      var price = double.tryParse(offerText) ?? 0;
      var quantity1 = double.tryParse(widget.quantityController.text) ?? 0;
      var sum = price / quantity1;
      widget.offerController.text = currencyFormatter.format(sum);
    } else if (_previouslySubmitted) {
      widget.finalofferController.text =
          widget.previousQuote?.offerPrice.toString() ?? "0";
      var offerText = cleanRupeeString(widget.finalofferController.text);
      var price = double.tryParse(offerText) ?? 0;
      var quantity1 = double.tryParse(widget.quantityController.text) ?? 0;
      var sum = price / quantity1;
      widget.offerController.text = currencyFormatter.format(sum);
    }
  }

  bool valuesCleared = false;

  // GST calculation methods
  double _calculateQuoteWithoutGst() {
    var offerText = cleanRupeeString(widget.finalofferController.text);
    return double.tryParse(offerText) ?? 0.0;
  }

  double _calculateQuoteWithGst() {
    var quoteWithoutGst = _calculateQuoteWithoutGst();
    var gstAmount = (quoteWithoutGst * (widget.gst ?? 0)) / 100;
    return quoteWithoutGst + gstAmount;
  }

  _clearValues() {
    widget.offerController.clear();
    widget.finalofferController.clear();
    widget.commentController.clear();
    valuesCleared = true;
  }

  Widget _buildOptionRow(String? key, String? value) {
    return SizedBox(
      width: MediaQuery.of(context).size.width - 100,
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              "$key: ",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value ?? "N/A",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionRowWithEdit(
      String? key, String? value, bool showEditButton) {
    return SizedBox(
      width: MediaQuery.of(context).size.width - 100,
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              "$key: ",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: showEditButton ? 2 : 3,
            child: Text(
              value ?? "N/A",
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (showEditButton)
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () {
                _showEditBrandDialog(context);
              },
            ),
        ],
      ),
    );
  }

  void _showEditBrandDialog(BuildContext context) {
    final TextEditingController brandController = TextEditingController(
      text: widget.offer.variant1OptionName == "Any"
          ? ""
          : widget.offer.variant1OptionName,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        var style = const TextStyle(fontWeight: FontWeight.bold);
        return AlertDialog(
          title: Text(
            'Enter Brand',
            style: style,
          ),
          content: TextField(
            controller: brandController,
            decoration: const InputDecoration(hintText: "Enter Brand Name"),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                'Cancel',
                style: style.copyWith(color: Colors.black),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text(
                'Ok',
                style: style.copyWith(color: Colors.black),
              ),
              onPressed: () {
                if (brandController.text.isNotEmpty) {
                  setState(() {
                    widget.offer.variant1OptionName = brandController.text;
                  });
                  Navigator.of(context).pop();
                } else {
                  alert("Please enter a brand name.");
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}

// class IndianRupeeFormatter extends TextInputFormatter {
//   @override
//   TextEditingValue formatEditUpdate(
//       TextEditingValue oldValue, TextEditingValue newValue) {
//     // Remove any non-digit characters
//     String newText = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

//     if (newText.isEmpty) {
//       return const TextEditingValue();
//     }

//     // Format the number as Indian Rupee with 2 decimal places
//     final formatter =
//         NumberFormat.currency(locale: 'en_IN', symbol: '₹', decimalDigits: 0);
//     String formattedText = formatter.format(double.parse(newText));

//     return TextEditingValue(
//       text: formattedText,
//       selection: TextSelection.collapsed(offset: formattedText.length),
//     );
//   }
// }

String cleanRupeeString(String rupeeString) {
  // Keep decimal points while removing rupee symbol and commas
  return rupeeString.replaceAll(RegExp(r'[₹,]'), '').trim();
}

class IndianRupeeFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // Step 1: Clean the input string to keep only digits and a potential decimal point.
    String cleanNumberStr = newValue.text.replaceAll(RegExp(r'[^\d.]'), '');

    // If the cleaned string is empty, return an empty value.
    if (cleanNumberStr.isEmpty) {
      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    // Step 2: Normalize and validate the cleaned number string.
    // Handle leading zeros for the integer part (e.g., "007" -> "7", "00.5" -> "0.5").
    // This is complex to do perfectly with regex for all cases like "0", "0.", "0.5", "007".
    // We'll first normalize dot and decimal places, then handle leading zeros.

    // Ensure only one decimal point and max two decimal places.
    int firstDotIndex = cleanNumberStr.indexOf('.');
    if (firstDotIndex != -1) {
      String beforeDot = cleanNumberStr.substring(0, firstDotIndex);
      String afterDot = cleanNumberStr.substring(firstDotIndex + 1);
      afterDot = afterDot.replaceAll('.', ''); // Remove any subsequent dots
      if (afterDot.length > 2) {
        afterDot = afterDot.substring(0, 2); // Limit to two decimal places
      }
      cleanNumberStr = '$beforeDot.$afterDot';
    }

    // Normalize leading zeros and cases like "." -> "0."
    if (cleanNumberStr.startsWith('.')) {
      // Input like ".5"
      cleanNumberStr = '0$cleanNumberStr'; // Becomes "0.5"
    } else if (cleanNumberStr.contains('.')) {
      // Input like "00.5" or "0.5"
      List<String> parts = cleanNumberStr.split('.');
      String integerPart = parts[0];
      if (integerPart.isEmpty) {
        // Should have been caught by startsWith('.') e.g. from ".5"
        integerPart = "0";
      } else if (integerPart.length > 1 && integerPart.startsWith('0')) {
        integerPart =
            integerPart.replaceFirst(RegExp(r'^0+(?=\d)'), ''); // "007" -> "7"
        if (integerPart.isEmpty) integerPart = "0"; // "00" -> "0"
      }
      cleanNumberStr = '$integerPart.${parts.length > 1 ? parts[1] : ""}';
    } else {
      // Integer input like "007" or "0"
      if (cleanNumberStr.length > 1 && cleanNumberStr.startsWith('0')) {
        cleanNumberStr = cleanNumberStr.replaceFirst(
            RegExp(r'^0+(?=\d)'), ''); // "007" -> "7"
        if (cleanNumberStr.isEmpty) {
          cleanNumberStr =
              "0"; // "00" -> "0" (though regex might not make it empty)
        }
      }
    }

    // If after all cleaning, it's just an empty string (e.g. from "00" -> "" if regex was too aggressive)
    // or became invalid, revert or set to a default.
    // For "0" or "0.0", tryParse will work.
    // If input was just ".", it will be "0." now due to `startsWith('.')` logic if it's not empty.
    // Let's ensure "." becomes "0." for formatting logic later.
    String effectiveCleanNumberStr = cleanNumberStr;
    if (cleanNumberStr == ".") {
      effectiveCleanNumberStr = "0."; // Standardize for formatting
    } else if (cleanNumberStr.isEmpty) {
      // Should not happen if initial check passed
      return const TextEditingValue(
          text: "", selection: TextSelection.collapsed(offset: 0));
    }

    // Step 3: Format the number string.
    final currencyFormatter =
        NumberFormat.currency(locale: 'en_IN', symbol: '₹', decimalDigits: 2);
    final numberPartFormatter = NumberFormat(
        "#,##,##0", "en_IN"); // For formatting integer part with Indian system

    String formattedText;
    bool endsWithDot = effectiveCleanNumberStr.endsWith('.');

    if (endsWithDot &&
        effectiveCleanNumberStr.indexOf('.') ==
            effectiveCleanNumberStr.length - 1) {
      // Input ends with a dot, like "123." or "0." (from an initial ".")
      String numberPartStr = effectiveCleanNumberStr.substring(
          0, effectiveCleanNumberStr.length - 1);
      if (numberPartStr.isEmpty) {
        numberPartStr = "0"; // Handles the "0." case where numberPart is "0"
      }

      double? val = double.tryParse(numberPartStr);
      if (val != null) {
        String formattedNumberPart = numberPartFormatter.format(val);
        formattedText = '₹$formattedNumberPart.';
      } else {
        // This should not be reached if numberPartStr is always a valid number or "0"
        formattedText = '₹'; // Fallback
      }
    } else {
      // Standard cases: "123", "123.4", "123.45"
      double? value = double.tryParse(effectiveCleanNumberStr);
      if (value != null) {
        formattedText = currencyFormatter.format(value);
      } else {
        // If parsing fails despite sanitization, revert to old value.
        return oldValue;
      }
    }

    // Step 4: Adjust cursor position.
    int newOffset = formattedText.length; // Default to end

    if (endsWithDot) {
      // If the formatted text is "₹123.", cursor at the end.
      newOffset = formattedText.length;
    } else if (effectiveCleanNumberStr.contains('.')) {
      // For inputs like "123.4" (formatted to "₹1,23.40") or "123.45" (formatted to "₹1,23.45")
      int dotInCleanStr = effectiveCleanNumberStr.indexOf('.');
      int digitsAfterDotInCleanStr =
          effectiveCleanNumberStr.length - (dotInCleanStr + 1);

      int dotInFormattedText = formattedText.indexOf('.');
      if (dotInFormattedText != -1) {
        if (digitsAfterDotInCleanStr == 1) {
          // Input "123.4", formatted "₹1,23.40", cursor "₹1,23.4|0"
          newOffset =
              dotInFormattedText + 1 + 1; // after the first decimal digit
        } else {
          // Input "123.45", formatted "₹1,23.45", cursor "₹1,23.45|"
          // Input "123" (integer), formatted "₹1,23.00", cursor "₹1,23.00|"
          newOffset = formattedText.length;
        }
      }
    }
    // For integers without a typed dot, newOffset remains formattedText.length (cursor at end).

    // Ensure offset is within bounds
    if (newOffset > formattedText.length) newOffset = formattedText.length;
    if (newOffset < 0) newOffset = 0;

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: newOffset),
    );
  }
}
